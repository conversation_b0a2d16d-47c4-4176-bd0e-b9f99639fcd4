package s3manager

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

type OvhProject struct {
	Description string `json:"description"`
	ProjectId   string `json:"project_id"`
}

// ovhProjectsCmd represents the ovh-projects command
var ovhProjectsCmd = &cobra.Command{
	Use:   "ovh-projects",
	Short: "List OVH projects",
	Long:  "List OVH projects using the configured OVH API credentials.",
	RunE:  runOvhProjects,
}

func init() {
	rootCmd.AddCommand(ovhProjectsCmd)
}

func runOvhProjects(cmd *cobra.Command, args []string) error {
	config := GetGlobalConfig()

	provider, err := ovh.NewProvider(config.OvhEndpoint, config.OvhApplicationKey, config.OvhApplicationSecret, config.OvhConsumerKey, strings.TrimSpace(config.OvhProjectId))
	if err != nil {
		return errors.Wrap(err, "failed to create OVH provider")
	}

	result, err := provider.ExecApi("get", "/cloud/project", "")
	if err != nil {
		return errors.Wrap(err, "failed to list OVH projects")
	}

	projectList := []OvhProject{}
	if resultList, ok := result.([]any); ok {
		for i, element := range resultList {
			if projectId, ok := element.(string); ok {
				project, err := provider.ExecApi("get", fmt.Sprintf("/cloud/project/%s", projectId), "")
				if err != nil {
					return errors.Wrapf(err, "failed to get project details for %s", projectId)
				}
				projectList = append(projectList, OvhProject{
					Description: project.(map[string]any)["description"].(string),
					ProjectId:   project.(map[string]any)["project_id"].(string),
				})
			} else {
				return fmt.Errorf("resultList element %d: %v (unexpected type: %T)", i, element, element)
			}
		}
	} else {
		return fmt.Errorf("unexpected result format, got type: %T", result)
	}

	for _, project := range projectList {
		fmt.Printf("%s   %s\n", project.Description, project.ProjectId)
	}

	// resultJSON, err := json.MarshalIndent(projectList, "", "  ")
	// if err != nil {
	// 	return errors.Wrap(err, "failed to marshal project list to JSON")
	// }
	// fmt.Printf("%s\n", string(resultJSON))

	return nil
}
